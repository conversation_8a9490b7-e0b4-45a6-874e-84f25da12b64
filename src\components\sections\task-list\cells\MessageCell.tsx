'use client';

import React, { useState } from 'react';
import { MessageCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useReminderStore } from '@/store/reminderStore';
import { TaskMessageModal } from '../cards/TaskMessageModal';
import type { Task } from '@/types';

interface MessageCellProps {
  task: Task;
  textClassName?: string;
}

export const MessageCell: React.FC<MessageCellProps> = ({ task }) => {
  const [open, setOpen] = useState(false);
  const { messages, markAsRead } = useReminderStore();

  // 过滤出当前任务的消息
  const taskMessages = messages.filter(msg => msg.taskId === task.id);

  // 未读消息数量
  const unreadCount = taskMessages.filter(msg => !msg.read).length;

  // 处理消息图标点击
  const handleMessageIconClick = () => {
    setOpen(true);
  };

  // 处理标记单个消息为已读
  const handleMarkAsRead = (messageId: string) => {
    markAsRead(messageId);
  };

  // 处理标记所有消息为已读
  const handleMarkAllAsRead = () => {
    taskMessages.forEach(msg => {
      if (!msg.read) {
        markAsRead(msg.id);
      }
    });
  };

  return (
    <div className="flex items-center justify-center h-full relative after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:shadow-[2px_0_4px_rgba(0,0,0,0.1)] after:content-[''] overflow-visible">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className="relative cursor-pointer p-1 hover:bg-gray-100 rounded transition-colors overflow-visible"
              onClick={handleMessageIconClick}
            >
              <MessageCircle className={cn(
                "h-4 w-4",
                unreadCount > 0 ? "text-blue-600" : "text-muted-foreground"
              )} />
              {/* 消息角标 - 优化显示样式，确保完整显示 */}
              {unreadCount > 0 && (
                <div className="message-badge absolute -top-1.5 -right-1.5 bg-red-500 text-white text-[9px] font-bold rounded-full min-w-[14px] h-[14px] flex items-center justify-center px-0.5 z-30 border border-white shadow-sm pointer-events-auto">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </div>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{unreadCount > 0 ? `${unreadCount} 条未读消息` : '查看消息'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* 使用与卡片模式相同的TaskMessageModal */}
      <TaskMessageModal
        open={open}
        onOpenChange={setOpen}
        task={task}
        onMarkAsRead={handleMarkAsRead}
        onMarkAllAsRead={handleMarkAllAsRead}
      />
    </div>
  );
};