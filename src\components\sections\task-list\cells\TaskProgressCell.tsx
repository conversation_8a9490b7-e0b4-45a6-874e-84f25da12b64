
// src/components/sections/task-list/cells/TaskProgressCell.tsx
'use client';

import React from 'react';
import type { Task } from '@/types';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface TaskProgressCellProps {
  task: Task;
  textClassName?: string;
}

export const TaskProgressCell: React.FC<TaskProgressCellProps> = ({ task, textClassName }) => {
  const progressValue = task.requiredVolume > 0 ? (task.completedVolume / task.requiredVolume) * 100 : 0;
  return (
    <div className="flex flex-col items-center justify-center h-full">
      <Progress value={progressValue} className="w-full h-2.5" />
      <span className={cn("text-[9px] mt-0.5", textClassName)}>{Math.round(progressValue)}%</span>
    </div>
  );
};
