
'use client';

import React, { useEffect, useCallback, useRef, useState, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { useQuery } from '@tanstack/react-query';
import { TopBar } from './top-bar';
import { LeftPanel } from './left-panel';
import { RightPanel } from './right-panel';
import type { Vehicle, Plant, Task } from '@/types';
import { useAppStore } from '@/store/appStore';
import { useUiStore, initializeUiStoreWithPlantData } from '@/store/uiStore';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { shallow } from 'zustand/shallow';
import { getStaticPlants } from '@/services/dataService';
import { taskStatusOptions as defaultTaskStatusOptions } from '@/data/mock-data';


const CrossPlantDispatchModal = dynamic(() =>
  import('@/components/modals/cross-plant-dispatch-modal').then((mod) => mod.CrossPlantDispatchModal)
);

const DynamicWorkerInitializer = dynamic(() =>
  import('@/components/layout/worker-initializer').then((mod) => mod.WorkerInitializer),
  { ssr: false }
);

const RIGHT_PANEL_DEFAULT_WIDTH = 320;
const RIGHT_PANEL_MIN_WIDTH = 280;
const LEFT_PANEL_MIN_WIDTH = 400;
const RIGHT_PANEL_WIDTH_STORAGE_KEY = 'appRightPanelWidth_v1';

const MemoizedTopBar = React.memo(TopBar);
const MemoizedLeftPanel = React.memo(LeftPanel);
const MemoizedRightPanel = React.memo(RightPanel);

interface MainLayoutProps {
  initialPlants: Plant[]; // Prop for server-fetched plants
}

export function MainLayout({ initialPlants }: MainLayoutProps) {
  const {
    fetchInitialData: fetchOtherData,
    dispatchVehicleToTask,
    cancelVehicleDispatch,
    confirmCrossPlantDispatch,
  } = useAppStore();

  const { data: plants = [], isLoading: isLoadingPlants, error: plantsError } = useQuery<Plant[]>({
    queryKey: ['plants'],
    queryFn: getStaticPlants, 
    initialData: initialPlants, 
    staleTime: Infinity,
  });

  const vehicles = useAppStore(state => state.vehicles, shallow);
  const allTasks = useAppStore(state => state.tasks, shallow);

  const selectedPlantId = useUiStore(state => state.selectedPlantId);
  const shouldPlayAlertSound = useUiStore(state => state.shouldPlayAlertSound);
  const { toast } = useToast();

  const [crossPlantDispatchInfo, setCrossPlantDispatchInfo] = useState<{
    vehicle: Vehicle | null;
    sourcePlant: Plant | null;
    targetPlant: Plant | null;
  }>({ vehicle: null, sourcePlant: null, targetPlant: null });
  const [isCrossPlantDispatchModalOpen, setIsCrossPlantDispatchModalOpen] = React.useState(false);

  const [rightPanelWidth, setRightPanelWidth] = useState<number>(RIGHT_PANEL_DEFAULT_WIDTH);
  const [isResizingPanels, setIsResizingPanels] = useState(false);
  const resizeDragStartRef = useRef<{ startX: number; initialWidth: number } | null>(null);
  const mainLayoutRef = useRef<HTMLDivElement>(null);
  const audioPlayedForCountRef = useRef<number>(0);

  useEffect(() => {
    if (plants.length > 0 && !selectedPlantId) {
      const initialDefaultPlantId = plants[0].id;
      const initialTaskStatusFilter = defaultTaskStatusOptions.find(opt => opt.label === '正在进行')?.value || 'InProgress';
      initializeUiStoreWithPlantData(initialDefaultPlantId, initialTaskStatusFilter);
    }
  }, [plants, selectedPlantId]);

  useEffect(() => {
    fetchOtherData();
  }, [fetchOtherData]);

  useEffect(() => {
    if (shouldPlayAlertSound > audioPlayedForCountRef.current) {
      const audio = document.getElementById('dispatch-alert-sound') as HTMLAudioElement;
      if (audio) {
        audio.play().catch(error => console.warn("Audio play failed:", error));
      }
      audioPlayedForCountRef.current = shouldPlayAlertSound;
    }
  }, [shouldPlayAlertSound]);

  useEffect(() => {
    const storedWidth = localStorage.getItem(RIGHT_PANEL_WIDTH_STORAGE_KEY);
    if (storedWidth) {
      const parsedWidth = parseInt(storedWidth, 10);
      if (!isNaN(parsedWidth) && parsedWidth >= RIGHT_PANEL_MIN_WIDTH) {
        setRightPanelWidth(parsedWidth);
      }
    }
  }, []);

  useEffect(() => {
    if (rightPanelWidth !== RIGHT_PANEL_DEFAULT_WIDTH) {
        localStorage.setItem(RIGHT_PANEL_WIDTH_STORAGE_KEY, rightPanelWidth.toString());
    }
  }, [rightPanelWidth]);

  const currentPlant = useMemo(() => plants.find(p => p.id === selectedPlantId), [plants, selectedPlantId]);

  const handleVehicleDispatchCallback = useCallback(async (vehicleId: string, taskId: string, productionLineId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    const task = allTasks.find(t => t.id === taskId);
    const plantName = currentPlant?.name || 'N/A';

    if (vehicle && task && (vehicle.status === 'pending' || vehicle.status === 'returned') && (vehicle.operationalStatus === 'normal' || vehicle.operationalStatus === undefined)) {
      const updatedVehicleResult = await dispatchVehicleToTask(vehicleId, taskId, productionLineId);
      if (updatedVehicleResult) {
        toast({
          title: '车辆已调度',
          description: `车辆 ${updatedVehicleResult.vehicleNumber} 已被调度到任务 ${task.taskNumber} (生产线 ${productionLineId}, 厂区: ${plantName}).`,
        });
      } else {
        toast({
          title: '调度失败',
          description: `车辆 ${vehicle.vehicleNumber} 无法调度，请检查车辆或任务状态。`,
          variant: 'destructive',
        });
      }
    } else {
      toast({
        title: '调度失败',
        description: `车辆 ${vehicle?.vehicleNumber || vehicleId} 无法调度 (当前状态: ${vehicle?.status}, 操作状态: ${vehicle?.operationalStatus || 'normal'}).`,
        variant: 'destructive',
      });
    }
  }, [vehicles, allTasks, currentPlant, toast, dispatchVehicleToTask]);

  const handleCancelDispatchCallback = useCallback((vehicleId: string) => {
    const vehicleToCancel = vehicles.find(v => v.id === vehicleId);
    if (!vehicleToCancel) return;

    cancelVehicleDispatch(vehicleId);
    toast({
      title: '调度已取消',
      description: `车辆 ${vehicleToCancel.vehicleNumber} 的调度已取消，已返回待发列表。`,
    });
  }, [vehicles, toast, cancelVehicleDispatch]);

  const handleInitiateCrossPlantDispatch = useCallback((vehicleId: string, targetPlantId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    const sourcePlant = plants.find(p => p.id === selectedPlantId);
    const targetPlantDetails = plants.find(p => p.id === targetPlantId);

    if (vehicle && sourcePlant && targetPlantDetails && sourcePlant.id !== targetPlantDetails.id) {
      setCrossPlantDispatchInfo({ vehicle, sourcePlant, targetPlant: targetPlantDetails });
      setIsCrossPlantDispatchModalOpen(true);
    } else {
      toast({
        title: '跨站调度错误',
        description: '无法发起跨站调度，车辆或厂区信息不完整，或目标厂区与当前厂区相同。',
        variant: 'destructive',
      });
    }
  }, [vehicles, selectedPlantId, toast, plants]);

  const handleConfirmCrossPlantDispatch = useCallback((vehicleId: string, targetPlantId: string, notes?: string) => {
    confirmCrossPlantDispatch(vehicleId, targetPlantId, notes);
    const vehicle = vehicles.find(v => v.id === vehicleId);
    const sourceP = plants.find(p => p.id === selectedPlantId);
    const targetP = plants.find(p => p.id === targetPlantId);
    toast({
      title: '跨站调度已提交',
      description: `车辆 ${vehicle?.vehicleNumber} 从 ${sourceP?.name} 调度到 ${targetP?.name}. 备注: ${notes || '无'}`,
    });
    setIsCrossPlantDispatchModalOpen(false);
  }, [confirmCrossPlantDispatch, vehicles, plants, selectedPlantId, toast]);

  const handleMouseDownOnDivider = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsResizingPanels(true);
    resizeDragStartRef.current = {
      startX: e.clientX,
      initialWidth: rightPanelWidth,
    };
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [rightPanelWidth]);

  const handleMouseMoveForPanelResize = useCallback((e: MouseEvent) => {
    if (!isResizingPanels || !resizeDragStartRef.current || !mainLayoutRef.current) return;
    const deltaX = e.clientX - resizeDragStartRef.current.startX;
    let newWidth = resizeDragStartRef.current.initialWidth - deltaX;

    const totalWidth = mainLayoutRef.current.offsetWidth;
    const maxWidth = totalWidth - LEFT_PANEL_MIN_WIDTH - 6;

    newWidth = Math.max(RIGHT_PANEL_MIN_WIDTH, Math.min(newWidth, maxWidth));
    setRightPanelWidth(newWidth);
  }, [isResizingPanels]);

  const handleMouseUpForPanelResize = useCallback(() => {
    setIsResizingPanels(false);
    resizeDragStartRef.current = null;
    if (typeof document !== 'undefined' && document.body) {
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    }
  }, []);

  useEffect(() => {
    if (isResizingPanels) {
      window.addEventListener('mousemove', handleMouseMoveForPanelResize);
      window.addEventListener('mouseup', handleMouseUpForPanelResize);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMoveForPanelResize);
      window.removeEventListener('mouseup', handleMouseUpForPanelResize);
      if (typeof document !== 'undefined' && document.body) {
        if (document.body.style.cursor === 'col-resize') {
          document.body.style.cursor = '';
        }
        if (document.body.style.userSelect === 'none') {
           document.body.style.userSelect = '';
        }
      }
    };
  }, [isResizingPanels, handleMouseMoveForPanelResize, handleMouseUpForPanelResize]);

  if (isLoadingPlants && plants.length === 0) { 
    return <div className="h-screen w-screen flex items-center justify-center">加载厂区信息...</div>;
  }
  if (plantsError) {
    return <div className="h-screen w-screen flex items-center justify-center">加载厂区信息失败: {plantsError.message}</div>;
  }

  return (
    <div ref={mainLayoutRef} className="h-screen w-screen flex flex-col overflow-hidden bg-background">
      <DynamicWorkerInitializer />
      <div className="flex-[0_0_auto] min-h-[80px] max-h-[110px] mb-1">
        <MemoizedTopBar
          plants={plants}
          onInitiateCrossPlantDispatch={handleInitiateCrossPlantDispatch}
        />
      </div>

      <div className="flex-1 flex overflow-hidden">
        <div style={{ flex: 1, minWidth: `${LEFT_PANEL_MIN_WIDTH}px` }} className="h-full">
          <MemoizedLeftPanel />
        </div>
        <div
          onMouseDown={handleMouseDownOnDivider}
          className={cn(
            "w-1.5 h-full bg-border cursor-col-resize flex items-center justify-center group hover:bg-primary/20 transition-colors",
            isResizingPanels && "bg-primary"
          )}
        >
           <div className={cn("w-px h-8 bg-muted-foreground group-hover:bg-primary transition-colors", isResizingPanels && "bg-primary-foreground")}></div>
        </div>
        <div style={{ width: `${rightPanelWidth}px`, flexShrink: 0 }} className="h-full">
          <MemoizedRightPanel />
        </div>
      </div>
      {isCrossPlantDispatchModalOpen && (
        <CrossPlantDispatchModal
          isOpen={isCrossPlantDispatchModalOpen}
          onOpenChange={setIsCrossPlantDispatchModalOpen}
          vehicle={crossPlantDispatchInfo.vehicle}
          sourcePlant={crossPlantDispatchInfo.sourcePlant}
          targetPlant={crossPlantDispatchInfo.targetPlant}
          onConfirm={handleConfirmCrossPlantDispatch}
        />
      )}
    </div>
  );
}
