
import type { Plant, Task, DeliveryOrder, Vehicle } from '@/types';
import { 
  generateMockTasks, 
  generateMockVehicles, 
  generateMockDeliveryOrders, 
  mockPlants as staticMockPlants 
} from '@/data/mock-data';

// Simulate async data fetching for read operations
export async function getTasks(): Promise<Task[]> {
  await new Promise(resolve => setTimeout(resolve, 50)); 
  return generateMockTasks();
}

export async function getVehicles(): Promise<Vehicle[]> {
  await new Promise(resolve => setTimeout(resolve, 50));
  return generateMockVehicles();
}

export async function getDeliveryOrders(): Promise<DeliveryOrder[]> {
  await new Promise(resolve => setTimeout(resolve, 50));
  return generateMockDeliveryOrders();
}

export function getStaticPlants(): Plant[] {
  return staticMockPlants;
}

// Simulate async data modification operations (CRUD-like)
export async function dispatchVehicleToTaskService(
  currentVehicles: Vehicle[],
  vehicleId: string,
  taskId: string,
  productionLineId: string
): Promise<Vehicle | null> {
  await new Promise(resolve => setTimeout(resolve, 50)); // Simulate network delay
  const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
  if (vehicleIndex > -1) {
    const updatedVehicle = {
      ...currentVehicles[vehicleIndex],
      status: 'outbound' as Vehicle['status'],
      assignedTaskId: taskId,
      assignedProductionLineId: productionLineId,
      productionStatus: 'queued' as Vehicle['productionStatus'], // Example initial production status
    };
    return updatedVehicle;
  }
  console.error(`dispatchVehicleToTaskService: Vehicle ${vehicleId} not found.`);
  return null;
}

export async function cancelVehicleDispatchService(
  currentVehicles: Vehicle[],
  vehicleId: string
): Promise<Vehicle | null> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
  if (vehicleIndex > -1) {
    const updatedVehicle = {
      ...currentVehicles[vehicleIndex],
      status: 'pending' as Vehicle['status'], // Or determine original status if tracked
      assignedTaskId: undefined,
      assignedProductionLineId: undefined,
      productionStatus: undefined,
      currentTripType: undefined,
      deliveryOrderId: undefined,
    };
    return updatedVehicle;
  }
  console.error(`cancelVehicleDispatchService: Vehicle ${vehicleId} not found.`);
  return null;
}

export async function reorderVehiclesInListService(
  currentVehicles: Vehicle[],
  draggedVehicleId: string,
  targetVehicleId: string,
  listStatus: Vehicle['status']
): Promise<Vehicle[]> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehiclesCopy = [...currentVehicles];
  const listToReorder = vehiclesCopy.filter(v => v.status === listStatus);
  const otherVehicles = vehiclesCopy.filter(v => v.status !== listStatus);

  const draggedItem = listToReorder.find(v => v.id === draggedVehicleId);
  if (!draggedItem) return currentVehicles; // Should not happen

  const remainingItems = listToReorder.filter(v => v.id !== draggedVehicleId);
  const targetIndex = remainingItems.findIndex(v => v.id === targetVehicleId);

  if (targetIndex === -1) { // Drop on the list but not on a specific card (e.g. end of list)
      remainingItems.push(draggedItem);
  } else {
      remainingItems.splice(targetIndex, 0, draggedItem);
  }
  
  return [...otherVehicles, ...remainingItems];
}

export async function confirmCrossPlantDispatchService(
  currentVehicles: Vehicle[],
  vehicleId: string,
  targetPlantId: string,
  notes?: string 
): Promise<Vehicle | null> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
  if (vehicleIndex > -1) {
    const updatedVehicle = {
      ...currentVehicles[vehicleIndex],
      plantId: targetPlantId,
      status: 'pending' as Vehicle['status'], // Assuming it becomes pending in the new plant
      assignedTaskId: undefined,
      assignedProductionLineId: undefined,
      // Potentially log notes or handle other cross-plant logic here
    };
    console.log(`Cross-plant dispatch: Vehicle ${vehicleId} to Plant ${targetPlantId}. Notes: ${notes || 'N/A'}`);
    return updatedVehicle;
  }
  console.error(`confirmCrossPlantDispatchService: Vehicle ${vehicleId} not found.`);
  return null;
}
