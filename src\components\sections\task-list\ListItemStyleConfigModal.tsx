import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { GripVertical, ChevronDown, ChevronRight, Eye, EyeOff, Palette, Layout, Type, Settings } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

// 文本颜色选项
const TEXT_COLORS = [
  { label: '默认', value: 'text-foreground' },
  { label: '主色', value: 'text-primary' },
  { label: '蓝色', value: 'text-blue-600' },
  { label: '绿色', value: 'text-green-600' },
  { label: '红色', value: 'text-red-600' },
  { label: '橙色', value: 'text-orange-600' },
  { label: '紫色', value: 'text-purple-600' },
  { label: '灰色', value: 'text-gray-600' },
  { label: '辅助色', value: 'text-muted-foreground' },
];

// 背景颜色选项
const BACKGROUND_COLORS = [
  { label: '透明', value: 'bg-transparent' },
  { label: '白色', value: 'bg-white' },
  { label: '浅灰', value: 'bg-gray-50' },
  { label: '蓝色浅', value: 'bg-blue-50' },
  { label: '绿色浅', value: 'bg-green-50' },
  { label: '红色浅', value: 'bg-red-50' },
  { label: '橙色浅', value: 'bg-orange-50' },
  { label: '紫色浅', value: 'bg-purple-50' },
  { label: '强调背景', value: 'bg-accent/20' },
  { label: '主色背景', value: 'bg-primary/10' },
];

// 字体大小选项
const FONT_SIZES = [
  { label: '小 (12px)', value: 'text-xs' },
  { label: '正常 (14px)', value: 'text-sm' },
  { label: '中等 (16px)', value: 'text-base' },
  { label: '大 (18px)', value: 'text-lg' },
  { label: '特大 (20px)', value: 'text-xl' },
];

// 字体粗细选项
const FONT_WEIGHTS = [
  { label: '细', value: 'font-light' },
  { label: '正常', value: 'font-normal' },
  { label: '中等', value: 'font-medium' },
  { label: '粗', value: 'font-semibold' },
  { label: '很粗', value: 'font-bold' },
];

// 字段样式配置
interface FieldStyle {
  fontSize: string;
  fontWeight: string;
  textColor: string;
}

// 模块样式配置
interface ModuleStyle {
  backgroundColor: string;
  visible: boolean;
  width?: string; // 用于调度车辆等可调宽度的模块
}

// 布局配置
interface LayoutConfig {
  itemHeight: string;
  itemPadding: string;
  itemGap: string;
  borderRadius: string;
}

interface FieldDef {
  id: string;
  label: string;
  group?: string;
  icon?: React.ReactNode;
  renderPriority?: number;
  width?: string;
}

// 模块定义
const MODULES = {
  header: '顶部状态栏',
  critical: '最重要信息',
  normal: '普通信息',
  dispatch: '调度车辆',
  production: '生产线',
} as const;

// 字段组定义（用于字段归属配置）
const FIELD_GROUPS = {
  critical: '最重要信息',
  basic: '基础信息',
  time: '时间信息',
  other: '其他信息',
  fixed: '固定位置',
};

interface ListItemStyleConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  fieldOrder: string[];
  fieldVisibility: Record<string, boolean>;
  fieldStyles: Record<string, FieldStyle>;
  moduleStyles: Record<string, ModuleStyle>;
  layoutConfig: LayoutConfig;
  onSave: (params: {
    fieldOrder: string[];
    fieldVisibility: Record<string, boolean>;
    fieldStyles: Record<string, FieldStyle>;
    moduleStyles: Record<string, ModuleStyle>;
    layoutConfig: LayoutConfig;
    progressBarFgColor: string;
    progressBarBgColor: string;
  }) => void;
  onCancel: () => void;
  progressBarFgColor: string;
  progressBarBgColor: string;
  allFields: FieldDef[];
}

export const ListItemStyleConfigModal: React.FC<ListItemStyleConfigModalProps> = ({
  open,
  onOpenChange,
  fieldOrder: initialOrder,
  fieldVisibility: initialVisibility,
  fieldStyles: initialFieldStyles,
  moduleStyles: initialModuleStyles,
  layoutConfig: initialLayoutConfig,
  onSave,
  onCancel,
  progressBarFgColor: initialFg,
  progressBarBgColor: initialBg,
  allFields,
}) => {
  // 状态管理
  const [fieldOrder, setFieldOrder] = useState<string[]>(Array.isArray(initialOrder) ? initialOrder : []);
  const [fieldVisibility, setFieldVisibility] = useState<Record<string, boolean>>({ ...initialVisibility });
  const [fieldStyles, setFieldStyles] = useState<Record<string, FieldStyle>>({ ...initialFieldStyles });
  const [moduleStyles, setModuleStyles] = useState<Record<string, ModuleStyle>>({ ...initialModuleStyles });
  const [layoutConfig, setLayoutConfig] = useState<LayoutConfig>({ ...initialLayoutConfig });
  const [progressBarFgColor, setProgressBarFgColor] = useState<string>(initialFg);
  const [progressBarBgColor, setProgressBarBgColor] = useState<string>(initialBg);

  // UI状态
  const [dragIndex, setDragIndex] = useState<number | null>(null);
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>(
    Object.keys(FIELD_GROUPS).reduce((acc, group) => ({ ...acc, [group]: true }), {})
  );
  const [expandedModules, setExpandedModules] = useState<Record<string, boolean>>(
    Object.keys(MODULES).reduce((acc, module) => ({ ...acc, [module]: true }), {})
  );
  const [activeTab, setActiveTab] = useState('layout');

  // 获取字段所属组
  const getFieldGroup = (id: string): string => {
    const field = allFields.find(f => f.id === id);
    return field?.group || 'other';
  };

  // 按组分组字段
  const getGroupedFields = () => {
    const groups: Record<string, string[]> = {};

    // 确保 fieldOrder 存在且为数组
    if (Array.isArray(fieldOrder)) {
      fieldOrder.forEach(id => {
        const group = getFieldGroup(id);
        if (!groups[group]) groups[group] = [];
        groups[group].push(id);
      });
    }

    return groups;
  };

  // 更新字段样式
  const updateFieldStyle = (fieldId: string, styleKey: keyof FieldStyle, value: string) => {
    setFieldStyles(prev => ({
      ...prev,
      [fieldId]: {
        ...prev[fieldId],
        [styleKey]: value
      }
    }));
  };

  // 更新模块样式
  const updateModuleStyle = (moduleId: string, styleKey: keyof ModuleStyle, value: string | boolean) => {
    setModuleStyles(prev => ({
      ...prev,
      [moduleId]: {
        ...prev[moduleId],
        [styleKey]: value
      }
    }));
  };

  // 更新布局配置
  const updateLayoutConfig = (key: keyof LayoutConfig, value: string) => {
    setLayoutConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 拖拽排序
  const handleDragStart = (idx: number) => setDragIndex(idx);
  const handleDragEnter = (idx: number) => {
    if (dragIndex === null || dragIndex === idx || !Array.isArray(fieldOrder)) return;
    const newOrder = [...fieldOrder];
    const [removed] = newOrder.splice(dragIndex, 1);
    newOrder.splice(idx, 0, removed);
    setFieldOrder(newOrder);
    setDragIndex(idx);
  };
  const handleDragEnd = () => setDragIndex(null);

  const handleVisibilityToggle = (id: string) => {
    setFieldVisibility(v => ({ ...v, [id]: !v[id] }));
  };

  const toggleGroupVisibility = (group: string) => {
    // 确保 fieldOrder 存在且为数组
    if (!Array.isArray(fieldOrder)) return;

    const groupFields = fieldOrder.filter(id => getFieldGroup(id) === group);
    const allVisible = groupFields.every(id => fieldVisibility[id]);

    const newVisibility = {...fieldVisibility};
    groupFields.forEach(id => {
      newVisibility[id] = !allVisible;
    });

    setFieldVisibility(newVisibility);
  };

  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group]
    }));
  };

  const toggleModule = (module: string) => {
    setExpandedModules(prev => ({
      ...prev,
      [module]: !prev[module]
    }));
  };

  const handleSave = () => {
    onSave({
      fieldOrder,
      fieldVisibility,
      fieldStyles,
      moduleStyles,
      layoutConfig,
      progressBarFgColor,
      progressBarBgColor,
    });
    onOpenChange(false);
  };

  // 分组字段
  const groupedFields = getGroupedFields();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            任务列表样式配置
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="layout" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="layout" className="flex items-center gap-1">
              <Layout className="w-4 h-4" />
              布局设置
            </TabsTrigger>
            <TabsTrigger value="modules" className="flex items-center gap-1">
              <Palette className="w-4 h-4" />
              模块样式
            </TabsTrigger>
            <TabsTrigger value="fields" className="flex items-center gap-1">
              <Type className="w-4 h-4" />
              字段样式
            </TabsTrigger>
            <TabsTrigger value="visibility" className="flex items-center gap-1">
              <Eye className="w-4 h-4" />
              显示设置
            </TabsTrigger>
          </TabsList>

          {/* 布局设置标签页 */}
          <TabsContent value="layout" className="space-y-6 max-h-[60vh] overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 整体布局 */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium flex items-center gap-2">
                  <Layout className="w-4 h-4" />
                  整体布局
                </h3>

                <div className="space-y-3">
                  <div>
                    <Label className="text-xs">行高</Label>
                    <Select value={layoutConfig.itemHeight} onValueChange={(value) => updateLayoutConfig('itemHeight', value)}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="min-h-[40px]">紧凑 (40px)</SelectItem>
                        <SelectItem value="min-h-[50px]">正常 (50px)</SelectItem>
                        <SelectItem value="min-h-[60px]">宽松 (60px)</SelectItem>
                        <SelectItem value="min-h-[70px]">很宽松 (70px)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-xs">内边距</Label>
                    <Select value={layoutConfig.itemPadding} onValueChange={(value) => updateLayoutConfig('itemPadding', value)}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="p-1">小 (4px)</SelectItem>
                        <SelectItem value="p-2">正常 (8px)</SelectItem>
                        <SelectItem value="p-3">大 (12px)</SelectItem>
                        <SelectItem value="p-4">很大 (16px)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-xs">元素间距</Label>
                    <Select value={layoutConfig.itemGap} onValueChange={(value) => updateLayoutConfig('itemGap', value)}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gap-1">小 (4px)</SelectItem>
                        <SelectItem value="gap-2">正常 (8px)</SelectItem>
                        <SelectItem value="gap-3">大 (12px)</SelectItem>
                        <SelectItem value="gap-4">很大 (16px)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-xs">圆角</Label>
                    <Select value={layoutConfig.borderRadius} onValueChange={(value) => updateLayoutConfig('borderRadius', value)}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="rounded-none">无圆角</SelectItem>
                        <SelectItem value="rounded-sm">小圆角</SelectItem>
                        <SelectItem value="rounded-md">正常圆角</SelectItem>
                        <SelectItem value="rounded-lg">大圆角</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* 调度车辆区域宽度 */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">调度车辆区域</h3>
                <div>
                  <Label className="text-xs">区域宽度</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Slider
                      value={[parseInt(moduleStyles.dispatch?.width?.replace('w-', '') || '48')]}
                      onValueChange={([value]) => updateModuleStyle('dispatch', 'width', `w-${value}`)}
                      max={80}
                      min={32}
                      step={4}
                      className="flex-1"
                    />
                    <span className="text-xs text-muted-foreground w-12">
                      {moduleStyles.dispatch?.width?.replace('w-', '') || '48'}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    调整调度车辆区域的宽度以容纳更多车辆卡片
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* 模块样式标签页 */}
          <TabsContent value="modules" className="space-y-6 max-h-[60vh] overflow-y-auto">
            <div className="space-y-4">
              {Object.entries(MODULES).map(([moduleId, moduleLabel]) => (
                <div key={moduleId} className="border rounded-lg overflow-hidden">
                  <div className="bg-muted/30 px-4 py-3 border-b">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium flex items-center gap-2">
                        <Palette className="w-4 h-4" />
                        {moduleLabel}
                      </h3>
                      <Switch
                        checked={moduleStyles[moduleId]?.visible !== false}
                        onCheckedChange={(checked) => updateModuleStyle(moduleId, 'visible', checked)}
                      />
                    </div>
                  </div>

                  {moduleStyles[moduleId]?.visible !== false && (
                    <div className="p-4 space-y-4">
                      <div>
                        <Label className="text-xs">背景颜色</Label>
                        <Select
                          value={moduleStyles[moduleId]?.backgroundColor || 'bg-transparent'}
                          onValueChange={(value) => updateModuleStyle(moduleId, 'backgroundColor', value)}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {BACKGROUND_COLORS.map(color => (
                              <SelectItem key={color.value} value={color.value}>
                                <div className="flex items-center gap-2">
                                  <div className={cn("w-4 h-4 rounded border", color.value)} />
                                  {color.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* 调度车辆模块特殊配置 */}
                      {moduleId === 'dispatch' && (
                        <div>
                          <Label className="text-xs">区域宽度</Label>
                          <div className="flex items-center space-x-2 mt-1">
                            <Slider
                              value={[parseInt(moduleStyles.dispatch?.width?.replace('w-', '') || '48')]}
                              onValueChange={([value]) => updateModuleStyle('dispatch', 'width', `w-${value}`)}
                              max={80}
                              min={32}
                              step={4}
                              className="flex-1"
                            />
                            <span className="text-xs text-muted-foreground w-16">
                              {moduleStyles.dispatch?.width?.replace('w-', '') || '48'} (12rem)
                            </span>
                          </div>
                        </div>
                      )}

                      {/* 预览区域 */}
                      <div className="mt-4">
                        <Label className="text-xs">预览</Label>
                        <div className={cn(
                          "mt-1 p-3 rounded border-2 border-dashed border-gray-300",
                          moduleStyles[moduleId]?.backgroundColor || 'bg-transparent'
                        )}>
                          <div className="text-xs text-muted-foreground">
                            {moduleLabel} 模块预览
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>

          {/* 字段样式标签页 */}
          <TabsContent value="fields" className="space-y-4 max-h-[60vh] overflow-y-auto">
            <div className="space-y-4">
              {allFields.map((field) => (
                <div key={field.id} className="border rounded-lg overflow-hidden">
                  <div className="bg-muted/30 px-4 py-2 border-b">
                    <div className="flex items-center gap-2">
                      {field.icon}
                      <span className="font-medium">{field.label}</span>
                      <span className="text-xs text-muted-foreground">({field.group})</span>
                    </div>
                  </div>

                  <div className="p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* 字体大小 */}
                    <div>
                      <Label className="text-xs">字体大小</Label>
                      <Select
                        value={fieldStyles[field.id]?.fontSize || 'text-sm'}
                        onValueChange={(value) => updateFieldStyle(field.id, 'fontSize', value)}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {FONT_SIZES.map(size => (
                            <SelectItem key={size.value} value={size.value}>
                              {size.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 字体粗细 */}
                    <div>
                      <Label className="text-xs">字体粗细</Label>
                      <Select
                        value={fieldStyles[field.id]?.fontWeight || 'font-normal'}
                        onValueChange={(value) => updateFieldStyle(field.id, 'fontWeight', value)}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {FONT_WEIGHTS.map(weight => (
                            <SelectItem key={weight.value} value={weight.value}>
                              {weight.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 文字颜色 */}
                    <div>
                      <Label className="text-xs">文字颜色</Label>
                      <Select
                        value={fieldStyles[field.id]?.textColor || 'text-foreground'}
                        onValueChange={(value) => updateFieldStyle(field.id, 'textColor', value)}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TEXT_COLORS.map(color => (
                            <SelectItem key={color.value} value={color.value}>
                              <div className="flex items-center gap-2">
                                <div className={cn("w-4 h-4 rounded border", color.value.replace('text-', 'bg-'))} />
                                {color.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* 预览 */}
                  <div className="px-4 pb-4">
                    <Label className="text-xs">预览</Label>
                    <div className="mt-1 p-2 border rounded">
                      <span className={cn(
                        fieldStyles[field.id]?.fontSize || 'text-sm',
                        fieldStyles[field.id]?.fontWeight || 'font-normal',
                        fieldStyles[field.id]?.textColor || 'text-foreground'
                      )}>
                        {field.label} 样式预览
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {/* 显示设置标签页 */}
          <TabsContent value="visibility" className="space-y-4 max-h-[60vh] overflow-y-auto">
            <div className="space-y-4">
              {/* 按组显示字段 */}
              {Object.entries(FIELD_GROUPS).map(([group, groupLabel]) => {
                const groupFields = groupedFields[group] || [];
                if (groupFields.length === 0) return null;

                // 检查所有字段是否都可见
                const allVisible = groupFields.every(id => fieldVisibility[id]);

                return (
                  <div key={group} className="border rounded-md overflow-hidden">
                    {/* 组标题 */}
                    <div className="flex items-center justify-between bg-muted/50 px-3 py-2">
                      <div
                        className="flex items-center cursor-pointer"
                        onClick={() => toggleGroup(group)}
                      >
                        {expandedGroups[group] ?
                          <ChevronDown className="w-4 h-4 mr-2" /> :
                          <ChevronRight className="w-4 h-4 mr-2" />
                        }
                        <span className="font-medium">{groupLabel}</span>
                        <span className="text-xs text-muted-foreground ml-2">({groupFields.length})</span>
                      </div>
                      <div className="flex items-center">
                        <Switch
                          checked={allVisible}
                          onCheckedChange={() => toggleGroupVisibility(group)}
                          className="ml-2"
                        />
                      </div>
                    </div>

                    {/* 组内字段 */}
                    {expandedGroups[group] && (
                      <div className="divide-y">
                        {groupFields.map((id) => {
                          const field = allFields.find(f => f.id === id);
                          if (!field) return null;
                          const actualIdx = fieldOrder.indexOf(id);

                          return (
                            <div
                              key={id}
                              className={cn(
                                "flex items-center px-3 py-2 hover:bg-accent/5 cursor-move transition-colors",
                                dragIndex === actualIdx ? "bg-accent/10" : ""
                              )}
                              draggable
                              onDragStart={() => handleDragStart(actualIdx)}
                              onDragEnter={() => handleDragEnter(actualIdx)}
                              onDragOver={e => e.preventDefault()}
                              onDragEnd={handleDragEnd}
                            >
                              <GripVertical className="w-4 h-4 mr-3 text-muted-foreground flex-shrink-0" />
                              <div className="flex items-center gap-2 flex-1">
                                {field.icon && <div className="text-muted-foreground">{field.icon}</div>}
                                <span>{field.label}</span>
                                {field.width && (
                                  <span className="text-xs text-muted-foreground">({field.width.replace('max-w-', '')})</span>
                                )}
                              </div>
                              <div className="flex items-center gap-2">
                                {fieldVisibility[id] ? <Eye className="w-4 h-4 text-green-600" /> : <EyeOff className="w-4 h-4 text-gray-400" />}
                                <Switch
                                  checked={!!fieldVisibility[id]}
                                  onCheckedChange={() => handleVisibilityToggle(id)}
                                  className="ml-2"
                                />
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={onCancel}>取消</Button>
          <Button onClick={handleSave}>保存</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};