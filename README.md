# 车辆调度管理系统

## 项目概述
这是一个基于Next.js的车辆调度管理系统，用于管理车辆调度、生产线任务和跨厂区调度操作。

## 技术栈
- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **UI库**: Radix UI + Tailwind CSS
- **状态管理**: Zustand
- **数据管理**: React Query
- **表单处理**: react-hook-form
- **图表**: recharts
- **AI集成**: Genkit AI
- **部署**: Firebase

## 核心功能
1. **车辆调度管理**
   - 车辆到任务的调度
   - 调度取消功能
   - 车辆列表排序

2. **跨厂区调度**
   - 跨厂区调度请求
   - 调度确认流程
   - 调度记录

3. **任务管理**
   - 任务进度跟踪
   - 自动任务更新模拟
   - 生产线分配

4. **用户界面**
   - 可调整面板布局
   - 响应式设计
   - 通知系统

## 项目结构
```
src/
├── ai/            # AI相关功能
├── app/           # Next.js App Router
├── assets/        # 静态资源
├── components/    # 可复用组件
├── constants/     # 常量定义
├── contexts/      # React上下文
├── data/          # 数据模型
├── hooks/         # 自定义Hook
├── lib/           # 工具函数
├── services/      # API服务
├── store/         # Zustand状态
├── styles/        # 全局样式
├── types/         # TypeScript类型
└── utils/         # 实用工具
```

## 开发指南
1. 安装依赖
```bash
npm install
```

2. 开发模式
```bash
npm run dev
```

3. 构建生产版本
```bash
npm run build
```

4. 启动生产服务器
```bash
npm run start
```

## 环境要求
- Node.js 18+
- npm 9+