// src/components/sections/task-list/task-list.tsx
'use client';

import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { useDrop } from 'react-dnd';
import type { ColumnDef, CellContext, ColumnSizingState, OnChangeFn } from '@tanstack/react-table';
import { shallow } from 'zustand/shallow';


import type {
  Task, Vehicle, CustomColumnDefinition, 
  StyleableColumnId, TaskColumnId,
  TaskListDensityMode, DensityStyleValues, VehicleDisplayMode,
  TaskListDisplayMode,
  ColumnTextStyle
} from '@/types';
import {
  MoreVertical, Settings, Settings2, Columns, FileUp, FileDown, RotateCcw, Rows, LayoutGrid, Palette, BarChart3,
  Grid3X3, ChevronDown, CheckCircle2,
  Maximize2} from 'lucide-react';

import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, DropdownMenuPortal, DropdownMenuTrigger, DropdownMenuCheckboxItem
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/store/appStore';
import { useUiStore } from '@/store/uiStore';
import { useFilteredTasks } from '@/hooks/useFilteredTasks';
import { useTaskListSettings } from '@/hooks/useTaskListSettings';
import { useTaskContextMenu } from '@/hooks/useTaskContextMenu';
import { useVehicleCardContextMenu } from '@/hooks/useVehicleCardContextMenu';
import { useCurrentPlantInfo } from '@/hooks/useCurrentPlantInfo';
import { ALL_TASK_COLUMNS_CONFIG } from './task-list.config';
import { allTextColorOptionsMap, allBackgroundColorOptionsMap } from '@/components/modals/column-specific-style-modal';
import { ItemTypes } from '@/constants/dndItemTypes';

const EnhancedTaskCardView = dynamic(() => import('./EnhancedTaskCardView').then(mod => mod.EnhancedTaskCardView));



import { useToast } from '@/hooks/use-toast';
import { SectionContainer } from '@/components/shared/section-container';
import { VirtualizedTable } from '@/components/ui/virtualized-table';
import { OptimizedGroupTable } from './components/optimized-group-table';



import { DispatchReminderCell } from './cells/DispatchReminderCell';
import { TaskProgressCell } from './cells/TaskProgressCell';
import { DispatchedVehiclesCell } from './cells/DispatchedVehiclesCell';
import { ProductionLinesCell } from './cells/ProductionLinesCell';
import { MessageCell } from './cells/MessageCell';
import { TaskListModals } from './task-list-modals';
import { TaskListContextMenus } from './task-list-context-menus';
import { TaskGroupConfigModal } from './modals/task-group-config-modal';

import { TaskGroupHeader } from './components/task-group-header';
import { groupTasks } from '@/utils/task-grouping';
import { TaskCardConfig, defaultTaskCardConfig } from '@/types/taskCardConfig';
import { TaskCardConfigModal } from './cards/TaskCardConfigModal';

interface DraggableVehicleItem {
  vehicle: Vehicle;
  index: number;
  statusList: 'pending' | 'returned';
  type: typeof ItemTypes.VEHICLE_CARD_DISPATCH;
}


export interface TaskListProps {
  productionLineCount: number;
}

const getStatusLabelProps = (status?: Task['dispatchStatus']) => {
  switch (status) {
    case 'New': return { label: '新任务', className: 'bg-blue-100 text-blue-700 dark:bg-blue-700 dark:text-blue-100' };
    case 'ReadyToProduce': return { label: '准备生产', className: 'bg-cyan-100 text-cyan-700 dark:bg-cyan-700 dark:text-cyan-100' };
    case 'RatioSet': return { label: '已设定配比', className: 'bg-purple-100 text-purple-700 dark:bg-purple-700 dark:text-purple-100' };
    case 'InProgress': return { label: '正在进行', className: 'bg-teal-500 text-white' };
    case 'Paused': return { label: '暂停', className: 'bg-orange-400 text-white' };
    case 'Completed': return { label: '已完成', className: 'bg-status-success-bg text-status-success-fg' };
    case 'Cancelled': return { label: '已撤销', className: 'bg-red-500 text-white' };
    default: return { label: status || '未知', className: 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-200' };
  }
};


const FAB_SIZE = 40;
const FAB_MARGIN = 16;

export function TaskList() {
  const allTasks = useAppStore(state => state.tasks, shallow);
  const allVehicles = useAppStore(state => state.vehicles, shallow);
  const dispatchVehicleToTask = useAppStore(state => state.dispatchVehicleToTask);
  const cancelVehicleDispatch = useAppStore(state => state.cancelVehicleDispatch);

  const { selectedPlantId, taskStatusFilter, vehicleDisplayMode, setVehicleDisplayMode } = useUiStore();
  const { plants, productionLineCount, isLoadingPlants: isLoadingPlantInfo } = useCurrentPlantInfo();
  const { toast } = useToast();

  const {
    settings, updateSetting, handleColumnVisibilityChange, handleColumnOrderChange,
    handleColumnTextStyleChange, handleColumnBackgroundChange,
    resetAllSettings, exportSettings, triggerImport, fileInputRef, isSettingsLoaded,
    isColumnVisibilityModalOpen, isColumnSpecificStyleModalOpen, editingColumnDef,
    openColumnVisibilityModal, closeColumnVisibilityModal, openColumnSpecificStyleModal, closeColumnSpecificStyleModal,
    isStyleEditorModalOpen, openStyleEditorModal, closeStyleEditorModal, handleVehiclesPerRowChange,
    // 分组相关
    handleUpdateGroupConfig,
    handleToggleGrouping,
    handleSetGroupBy,
    handleToggleGroupCollapse,
    isGroupConfigModalOpen,
    setIsGroupConfigModalOpen,
  } = useTaskListSettings();

  const {
    displayMode, density, enableZebraStriping, columnOrder,
    columnTextStyles, columnBackgrounds, inTaskVehicleCardStyles,
  } = settings;

  const {
    isTaskContextMenuOpen, taskContextMenuPosition, contextMenuTaskData, openTaskContextMenu, closeTaskContextMenu,
    isTankerNoteModalOpen, selectedTaskForTankerNote, openTankerNoteModal, closeTankerNoteModal,
    isReminderConfigModalOpen, selectedTaskForReminderConfig, openReminderConfigModal, closeReminderConfigModal,
  } = useTaskContextMenu();

  const {
    isContextMenuOpen: isVehicleCardContextMenuOpen, contextMenuPosition: vehicleCardContextMenuPosition,
    contextMenuData: vehicleCardContextMenuContext, openContextMenu: openVehicleCardContextMenu,
    closeContextMenu: closeVehicleCardContextMenu, isDeliveryOrderDetailsModalOpen,
    selectedVehicleForDeliveryOrder, selectedTaskForDeliveryOrder, openDeliveryOrderDetailsModal, closeDeliveryOrderDetailsModal,
  } = useVehicleCardContextMenu();

  const fabPosition = useMemo(() => ({ x: FAB_MARGIN, y: FAB_MARGIN }), []);
  const fabRef = useRef<HTMLButtonElement>(null);
  const taskListBoundaryRef = useRef<HTMLDivElement>(null);

  const filteredTasks = useFilteredTasks();

  // 分组数据计算 - 优化依赖项以减少重新计算
  const taskGroups = useMemo(() => {
    return groupTasks(filteredTasks, settings.groupConfig);
  }, [
    filteredTasks, 
    settings.groupConfig.enabled,
    settings.groupConfig.groupBy,
    settings.groupConfig.defaultCollapsed,
    settings.groupConfig.sortOrder
  ]);

  // 分组统计
  const groupStats = useMemo(() => {
    const totalGroups = taskGroups.length;
    const collapsedGroups = taskGroups.filter(group => group.collapsed).length;
    return { totalGroups, collapsedGroups };
  }, [taskGroups]);

  // 展开的任务列表（用于表格显示）
  const expandedTasks = useMemo(() => {
    if (!settings.groupConfig.enabled) {
      return filteredTasks;
    }
    return taskGroups
      .filter(group => !group.collapsed)
      .flatMap(group => group.tasks);
  }, [taskGroups, filteredTasks, settings.groupConfig.enabled]);


  const densityStylesConfig: Record<Exclude<TaskListDensityMode, 'table' | 'card'>, DensityStyleValues> = {
    loose: {
      headerPaddingX: 'px-2', headerPaddingY: 'py-1.5', headerHeight: 'h-7', headerFontSize: 'text-sm',
      cellPaddingX: 'px-2', cellPaddingY: 'py-1.5', cellFontSize: 'text-sm', cellFontWeight: 'font-normal',
      productionLineBoxSize: 'w-10 h-10', productionLineBoxFontSize: 'text-xs',
      productionLineBoxNumericWidth: 40, productionLineBoxGap: 6, cellHorizontalPaddingNumeric: 8,
    },
    normal: {
      headerPaddingX: 'px-1.5', headerPaddingY: 'py-1', headerHeight: 'h-6', headerFontSize: 'text-[12px]',
      cellPaddingX: 'px-1.5', cellPaddingY: 'py-1', cellFontSize: 'text-[12px]', cellFontWeight: 'font-normal',
      productionLineBoxSize: 'w-8 h-8', productionLineBoxFontSize: 'text-[10px]',
      productionLineBoxNumericWidth: 32, productionLineBoxGap: 4, cellHorizontalPaddingNumeric: 6,
    },
    compact: {
      headerPaddingX: 'px-1', headerPaddingY: 'py-0.5', headerHeight: 'h-5', headerFontSize: 'text-[12px]',
      cellPaddingX: 'px-1', cellPaddingY: 'py-0.5', cellFontSize: 'text-[12px]', cellFontWeight: 'font-normal',
      productionLineBoxSize: 'w-7 h-7', productionLineBoxFontSize: 'text-[10px]',
      productionLineBoxNumericWidth: 28, productionLineBoxGap: 2, cellHorizontalPaddingNumeric: 4,
    },
  };

  const currentDensityValues = useMemo(() => {
    return densityStylesConfig[settings.density] || densityStylesConfig.normal;
  }, [settings.density]);

  const didShowResizeHintRef = useRef(false);

  const handleDropOnProductionLine = useCallback(async (vehicle: Vehicle, taskId: string, lineId: string) => {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) {
      toast({ title: "调度失败", description: "未找到目标任务。", variant: "destructive" });
      return;
    }
    if (task.dispatchStatus !== 'InProgress') {
      toast({ title: "调度失败", description: `任务 "${task.taskNumber}" 当前状态为 "${getStatusLabelProps(task.dispatchStatus).label}"，无法调度车辆。`, variant: "destructive" });
      return;
    }
    if (!vehicle) {
      toast({ title: "调度失败", description: "拖拽的车辆信息丢失。", variant: "destructive" });
      return;
    }

    const updatedVehicleResult = await dispatchVehicleToTask(vehicle.id, taskId, lineId);

    if (updatedVehicleResult) {
      const plantName = plants.find(p => p.id === selectedPlantId)?.name || selectedPlantId || 'N/A';
      toast({
        title: '车辆已调度',
        description: `车辆 ${updatedVehicleResult.vehicleNumber} 已成功调度到任务 ${task.taskNumber} (生产线 ${lineId}, 厂区: ${plantName}).`,
      });
    } else {
      toast({
        title: "调度失败",
        description: `无法调度车辆 ${vehicle.vehicleNumber} 到生产线 ${lineId}。请重试。`,
        variant: "destructive",
      });
    }
  }, [allTasks, dispatchVehicleToTask, toast, getStatusLabelProps, plants, selectedPlantId]);

  const handleOpenGroupConfig = useCallback(() => {
    setIsGroupConfigModalOpen(true);
  }, [setIsGroupConfigModalOpen]);

  // 深度合并配置的辅助函数
  const mergeTaskCardConfig = (defaultConfig: TaskCardConfig, savedConfig: any): TaskCardConfig => {
    if (!savedConfig || typeof savedConfig !== 'object') {
      return defaultConfig;
    }

    return {
      style: { ...defaultConfig.style, ...savedConfig.style },
      areas: {
        top: {
          ...defaultConfig.areas.top,
          ...savedConfig.areas?.top,
          fields: {
            ...defaultConfig.areas.top.fields,
            ...savedConfig.areas?.top?.fields
          }
        },
        vehicle: {
          ...defaultConfig.areas.vehicle,
          ...savedConfig.areas?.vehicle,
          fields: {
            ...defaultConfig.areas.vehicle.fields,
            ...savedConfig.areas?.vehicle?.fields
          }
        },
        content: {
          ...defaultConfig.areas.content,
          ...savedConfig.areas?.content,
          fields: {
            ...defaultConfig.areas.content.fields,
            ...savedConfig.areas?.content?.fields
          }
        },
        bottom: {
          ...defaultConfig.areas.bottom,
          ...savedConfig.areas?.bottom,
          fields: {
            ...defaultConfig.areas.bottom.fields,
            ...savedConfig.areas?.bottom?.fields
          }
        }
      }
    };
  };

  // 任务卡片配置状态 - 支持持久化
  const [taskCardConfig, setTaskCardConfig] = useState<TaskCardConfig>(defaultTaskCardConfig);
  const [isCardConfigLoaded, setIsCardConfigLoaded] = useState(false);

  // 加载任务卡片配置
  useEffect(() => {
    // 任务卡片配置现在通过useTaskListSettings管理
    setTaskCardConfig(defaultTaskCardConfig);
    setIsCardConfigLoaded(true);
  }, []);

  const handleTaskCardConfigChange = useCallback((config: TaskCardConfig) => {
    setTaskCardConfig(config);
    // 任务卡片配置保存逻辑可以在这里添加
    // 目前通过useTaskListSettings统一管理
  }, []);


  const [{ isOverTaskList, canDropOnTaskList }] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item: DraggableVehicleItem, monitor) => {
      if (!monitor.didDrop()) {
        toast({
          title: "操作无效",
          description: "请将车辆拖拽到具体的生产线上或厂区标签。",
          variant: "default",
        });
      }
    },
    collect: (monitor) => ({
      isOverTaskList: monitor.isOver({ shallow: true }),
      canDropOnTaskList: monitor.canDrop(),
    }),
  });


  const getCellTextClasses = useCallback((columnId: StyleableColumnId): string => {
    const styles = columnTextStyles[columnId];
    const classes: string[] = [];
    if (styles?.color && styles.color !== 'default') {
      const colorOption = allTextColorOptionsMap.get(styles.color);
      classes.push(colorOption ? colorOption.className : 'text-foreground');
    } else {
      classes.push('text-foreground');
    }
    if (styles?.fontSize && styles.fontSize !== 'default') classes.push(styles.fontSize);
    else classes.push(currentDensityValues.cellFontSize);

    if (styles?.fontWeight && styles.fontWeight !== 'default') classes.push(styles.fontWeight);
    else classes.push(currentDensityValues.cellFontWeight);

    return cn(classes);
  }, [columnTextStyles, currentDensityValues]);


  const estimateRowHeight = useCallback((): number => {
    if (settings.density === 'compact') return 36;
    if (settings.density === 'loose') return 46;
    return 42; // Normal
  }, [settings.density]);


  const getColumnBackgroundProps = useCallback((columnId: string, isHeader: boolean, isFixed: boolean): { style: React.CSSProperties, className: string } => {
    const bgSetting = columnBackgrounds[columnId as StyleableColumnId];
    const bgOption = bgSetting ? allBackgroundColorOptionsMap.get(bgSetting) : null;
    let style: React.CSSProperties = {};
    let className = '';

    if (bgOption) {
      if (isFixed && bgOption.specificSolidColor) {
        style.backgroundColor = bgOption.specificSolidColor;
      } else if (bgOption.themeClassName) {
        className = bgOption.themeClassName;
      } else if (bgOption.specificSolidColor) {
        style.backgroundColor = bgOption.specificSolidColor;
      }
    } else if (isFixed) {
      // 为固定列设置默认的不透明背景色
      const stickyStyle = settings.tableStyleConfig?.stickyColumnStyle;
      if (stickyStyle?.backgroundColor) {
        // 如果配置中有背景色，优先使用配置的值
        if (stickyStyle.backgroundColor.startsWith('bg-')) {
          // 如果是Tailwind类名，添加到className
          className = stickyStyle.backgroundColor;
        } else {
          // 如果是颜色值，添加到style
          style.backgroundColor = stickyStyle.backgroundColor;
        }
      } else {
        // 设置默认的不透明白色背景
        style.backgroundColor = '#ffffff';
        // 添加边框和阴影以增强视觉效果
        style.borderRight = '1px solid #e2e8f0';
        style.boxShadow = '2px 0 4px -1px rgba(0, 0, 0, 0.1)';
      }
    }
    return { style, className };
  }, [columnBackgrounds, settings.tableStyleConfig]);


  const tableColumns = useMemo<ColumnDef<Task>[]>(() => {
    return ALL_TASK_COLUMNS_CONFIG.map(colDef => {
      const baseCol: ColumnDef<Task> = {
        id: colDef.id,
        accessorKey: colDef.id,
        header: () => colDef.label,
        size: settings.columnWidths[colDef.id] || colDef.defaultWidth || 150,
        minSize: colDef.id === 'dispatchedVehicles' ? 200 : (colDef.minWidth || 50),
        maxSize: colDef.id === 'dispatchedVehicles' ? 600 : colDef.maxWidth,
        enableResizing: colDef.isResizable !== false,
        enableHiding: !colDef.isMandatory,
        meta: { customDef: { ...colDef, getColumnBackgroundProps, densityStyles: currentDensityValues } },
        cell: (info: CellContext<Task, any>) => {
          const task = info.row.original;
          const value = info.getValue();
          const columnId = info.column.id as StyleableColumnId;

          switch (columnId) {
            case 'dispatchReminder':
              return <DispatchReminderCell task={task} textClassName={getCellTextClasses(columnId)} />;
            case 'messages':
              return <MessageCell task={task} textClassName={getCellTextClasses(columnId)} />;
            case 'completedProgress':
              return <TaskProgressCell task={task} textClassName={getCellTextClasses(columnId)} />;
            case 'dispatchedVehicles':
              const taskSpecificVehicles = allVehicles.filter(v => v.assignedTaskId === task.id);
              return (
                <DispatchedVehiclesCell
                  task={task}
                  taskVehicles={taskSpecificVehicles}
                  vehicleDisplayMode={vehicleDisplayMode}
                  inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                  productionLineCount={productionLineCount}
                  density={settings.density}
                  onCancelDispatch={cancelVehicleDispatch}
                  onOpenStyleEditor={openStyleEditorModal}
                  onOpenDeliveryOrderDetails={(vehicle, currentTask) => openDeliveryOrderDetailsModal(vehicle, currentTask)}
                  onOpenContextMenu={(e, vehicle, currentTask) => openVehicleCardContextMenu(e, vehicle, currentTask)}
                />
              );
            case 'productionLines':
              return (
                <ProductionLinesCell
                  task={task}
                  productionLineCount={productionLineCount}
                  densityStyles={currentDensityValues}
                  onDropVehicleOnLine={handleDropOnProductionLine}
                />
              );
            case 'supplyTime':
              // 供砼时间显示为：供砼日期+供砼时间
              const combinedSupplyTime = `${task.supplyDate || ''} ${task.supplyTime || ''}`.trim();
              return (
                <span
                  className={cn("truncate block w-full h-full", getCellTextClasses(columnId))}
                  title={combinedSupplyTime}
                >
                  {combinedSupplyTime}
                </span>
              );
            default:
              return <span className={cn("truncate block w-full h-full", getCellTextClasses(columnId))}>{String(value ?? '')}</span>;
          }
        }
      };
      return baseCol;
    });
  }, [
    settings.columnWidths, settings.density, allVehicles, vehicleDisplayMode, inTaskVehicleCardStyles, productionLineCount,
    cancelVehicleDispatch, openStyleEditorModal, openDeliveryOrderDetailsModal, openVehicleCardContextMenu,
    getCellTextClasses, currentDensityValues, getColumnBackgroundProps, handleDropOnProductionLine
  ]);

  const tableTotalWidth = useMemo(() => {
    return tableColumns.reduce((sum, col) => {
      const customDef = (col.meta as any)?.customDef as CustomColumnDefinition | undefined;
      const colId = col.id as string;
      const isVisible = settings.columnVisibility[colId] !== false;
      return sum + (isVisible ? (settings.columnWidths[colId] || customDef?.defaultWidth || 150) : 0);
    }, 0);
  }, [tableColumns, settings.columnWidths, settings.columnVisibility]);




  const handleHeaderDoubleClick = useCallback((event: React.MouseEvent, columnDef: CustomColumnDefinition) => {
    event.preventDefault(); event.stopPropagation();
    if (displayMode === 'table') {
      if (columnDef.id === 'dispatchedVehicles' || columnDef.id === 'productionLines') {
        openColumnSpecificStyleModal(columnDef);
      } else if (columnDef.isStyleable !== false) {
        openColumnSpecificStyleModal(columnDef);
      } else {
        toast({ title: "样式设置", description: `列 \"${columnDef.label}\" 不支持单独的样式设置。`, variant: "default" });
      }
    }
  }, [displayMode, openColumnSpecificStyleModal, toast]);

  const handleHeaderContextMenu = useCallback((event: React.MouseEvent, columnDef: CustomColumnDefinition) => {
    event.preventDefault();
    const columnId = columnDef.id as TaskColumnId;
    
    // 检查该列是否允许分组
    const isAllowed = settings.groupConfig.allowedGroupColumns.includes(columnId);
    const isDisallowed = settings.groupConfig.disallowedGroupColumns.includes(columnId);
    
    if (isDisallowed || !isAllowed) {
      toast({ 
        title: "分组限制", 
        description: `列 \"${columnDef.label}\" 不支持分组操作。`, 
        variant: "default" 
      });
      return;
    }
    
    // 切换分组
    const newGroupBy = settings.groupConfig.groupBy === columnId ? 'none' : columnId;
    const newEnabled = newGroupBy !== 'none';
    
    updateSetting('groupConfig', {
      ...settings.groupConfig,
      groupBy: newGroupBy,
      enabled: newEnabled
    });
    
    toast({ 
      title: newEnabled ? "启用分组" : "取消分组", 
      description: newEnabled ? `已按 \"${columnDef.label}\" 分组` : "已取消分组", 
      variant: "default" 
    });
  }, [settings.groupConfig, updateSetting, toast]);

  /**
   * 处理取消分组操作
   */
  const handleCancelGrouping = useCallback(() => {
    updateSetting('groupConfig', {
      ...settings.groupConfig,
      groupBy: 'none',
      enabled: false
    });
    
    toast({ 
      title: "取消分组", 
      description: "已取消任务分组", 
      variant: "default" 
    });
  }, [settings.groupConfig, updateSetting, toast]);

  // 卡片配置状态
  const [cardConfig, setCardConfig] = useState({
    size: 'medium' as 'small' | 'medium' | 'large',
    layout: 'standard' as 'compact' | 'standard' | 'detailed' | 'minimal',
    theme: 'default' as 'default' | 'modern' | 'glass' | 'gradient' | 'dark',
    spacing: 'normal' as 'tight' | 'normal' | 'loose',
    borderRadius: 'medium' as 'none' | 'small' | 'medium' | 'large' | 'full',
    shadow: 'medium' as 'none' | 'small' | 'medium' | 'large' | 'glow',
    animation: 'smooth' as 'none' | 'subtle' | 'smooth' | 'bouncy',
    columns: 'auto' as 'auto' | '1' | '2' | '3' | '4' | '5' | '6',
  });

  const [taskCardConfigModalOpen, setTaskCardConfigModalOpen] = useState(false);

  // 获取列图标
  const getColumnIcon = (columnId: string): string => {
    const iconMap: Record<string, string> = {
      'projectName': '🏗️',
      'strength': '💪',
      'pouringMethod': '🔧',
      'supplyDate': '📅',
      'pumpTruck': '🚛',
      'constructionUnit': '🏢',
      'constructionSite': '📍',
      'dispatchStatus': '🔄',
      'deliveryStatus': '🚚',
      'plantId': '🏭',
    };
    return iconMap[columnId] || '📋';
  };

  /**
   * 渲染卡片模式操作栏
   */
  const renderCardModeActions = () => {
    if (displayMode !== 'card') return null;

    return (
      <div className="flex items-center gap-2">
        {/* 任务数量显示 */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <LayoutGrid className="w-4 h-4" />
          <span>{filteredTasks.length} 个任务</span>
        </div>

       

        {/* 分组按钮 - 改进版 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant={settings.groupConfig.enabled ? "default" : "outline"}
              size="sm"
              className="h-6 gap-1"
              title={settings.groupConfig.enabled ? '分组设置' : '启用分组'}
            >
              <Grid3X3 className="w-3 h-3" />
              {settings.groupConfig.enabled && <ChevronDown className="w-2 h-2" />}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>分组设置</DropdownMenuLabel>
            <DropdownMenuSeparator />

            {/* 取消分组选项 */}
            <DropdownMenuItem
              onClick={handleCancelGrouping}
              className={!settings.groupConfig.enabled ? 'bg-accent' : ''}
            >
              <div className="flex items-center gap-2">
                <span>📋</span>
                <span>不分组</span>
              </div>
            </DropdownMenuItem>

            <DropdownMenuSeparator />
            <DropdownMenuLabel>按列分组</DropdownMenuLabel>

            {/* 可分组的列选项 */}
            {settings.groupConfig.allowedGroupColumns.map((columnId) => {
              const columnDef = ALL_TASK_COLUMNS_CONFIG.find(col => col.id === columnId);
              if (!columnDef) return null;

              const isSelected = settings.groupConfig.groupBy === columnId;

              return (
                <DropdownMenuItem
                  key={columnId}
                  onClick={() => {
                    updateSetting('groupConfig', {
                      ...settings.groupConfig,
                      groupBy: columnId,
                      enabled: true
                    });
                    toast({
                      title: "启用分组",
                      description: `已按 "${columnDef.label}" 分组`,
                      variant: "default"
                    });
                  }}
                  className={isSelected ? 'bg-accent' : ''}
                >
                  <div className="flex items-center gap-2">
                    <span>{getColumnIcon(columnId)}</span>
                    <span>{columnDef.label}</span>
                    {isSelected && <CheckCircle2 className="w-3 h-3 ml-auto" />}
                  </div>
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 新的任务卡片配置按钮 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setTaskCardConfigModalOpen(true)}
          className="h-6"
          title="任务卡片配置"
        >
          <Settings className="w-3 h-3" />
        </Button>


      </div>
    );
  };

  /**
   * 渲染header操作按钮
   */
  const renderHeaderActions = () => {
    if (!isSettingsLoaded) return null;

    return (
      <div className="flex items-center gap-2">
        {/* 卡片模式操作栏 */}
        {renderCardModeActions()}
        
        {/* 通用设置下拉菜单 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-6">
              <MoreVertical className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Settings2 className="mr-2 h-3.5 w-3.5" />视图设置
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <DropdownMenuLabel>显示模式</DropdownMenuLabel>
                  <DropdownMenuRadioGroup value={displayMode} onValueChange={(value) => updateSetting('displayMode', value as TaskListDisplayMode)}>
                    <DropdownMenuRadioItem value="table"><Rows className="mr-2 h-3.5 w-3.5" />表格</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="card"><LayoutGrid className="mr-2 h-3.5 w-3.5" />卡片</DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>密度</DropdownMenuLabel>
                  <DropdownMenuRadioGroup value={density} onValueChange={(value) => updateSetting('density', value as Exclude<TaskListDensityMode, '' | 'card'>)}>
                    <DropdownMenuRadioItem value="compact">紧凑</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="normal">标准</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="loose">宽松</DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem checked={enableZebraStriping} onCheckedChange={(checked) => updateSetting('enableZebraStriping', !!checked)}>斑马条纹</DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>车辆显示</DropdownMenuLabel>
                  <DropdownMenuRadioGroup value={vehicleDisplayMode} onValueChange={(value) => setVehicleDisplayMode(value as VehicleDisplayMode)}>
                    <DropdownMenuRadioItem value="licensePlate">车牌号</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="internalId">内部编号</DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
            <DropdownMenuItem onClick={() => openColumnVisibilityModal()}><Columns className="mr-2 h-3.5 w-3.5" />列显示与排序</DropdownMenuItem>
            <DropdownMenuItem onClick={handleOpenGroupConfig}><BarChart3 className="mr-2 h-3.5 w-3.5" />分组设置</DropdownMenuItem>
            <DropdownMenuItem onClick={() => openStyleEditorModal()}><Palette className="mr-2 h-3.5 w-3.5" />车卡样式</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={triggerImport}><FileUp className="mr-2 h-3.5 w-3.5" />导入样式</DropdownMenuItem>
            <DropdownMenuItem onClick={exportSettings}><FileDown className="mr-2 h-3.5 w-3.5" />导出样式</DropdownMenuItem>
            <DropdownMenuItem onClick={resetAllSettings}><RotateCcw className="mr-2 h-3.5 w-3.5" />恢复默认</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  };

  const fabComponent = displayMode === 'table' && isSettingsLoaded && (
    <DropdownMenu>
      <DropdownMenuTrigger asChild><Button ref={fabRef} size="icon" className="rounded-full shadow-lg absolute z-40" style={{ top: fabPosition.y, left: fabPosition.x, width: `${FAB_SIZE}px`, height: `${FAB_SIZE}px` }} title="更多"><MoreVertical className="h-5 w-5" /></Button></DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuSub><DropdownMenuSubTrigger><Settings2 className="mr-2 h-3.5 w-3.5" />视图设置</DropdownMenuSubTrigger>
          <DropdownMenuPortal><DropdownMenuSubContent>
            <DropdownMenuLabel>显示模式</DropdownMenuLabel>
            <DropdownMenuRadioGroup value={displayMode} onValueChange={(value) => updateSetting('displayMode', value as TaskListDisplayMode)}>
              <DropdownMenuRadioItem value="table"><Rows className="mr-2 h-3.5 w-3.5" />表格</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="card"><LayoutGrid className="mr-2 h-3.5 w-3.5" />卡片</DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>密度</DropdownMenuLabel>
            <DropdownMenuRadioGroup value={density} onValueChange={(value) => updateSetting('density', value as Exclude<TaskListDensityMode, '' | 'card'>)}>
              <DropdownMenuRadioItem value="compact">紧凑</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="normal">标准</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="loose">宽松</DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
            <DropdownMenuSeparator />
            <DropdownMenuCheckboxItem checked={enableZebraStriping} onCheckedChange={(checked) => updateSetting('enableZebraStriping', !!checked)}>斑马条纹</DropdownMenuCheckboxItem>

            <DropdownMenuSeparator />
            <DropdownMenuLabel>车辆显示</DropdownMenuLabel>
            <DropdownMenuRadioGroup value={vehicleDisplayMode} onValueChange={(value) => setVehicleDisplayMode(value as VehicleDisplayMode)}>
              <DropdownMenuRadioItem value="licensePlate">车牌号</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="internalId">内部编号</DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent></DropdownMenuPortal>
        </DropdownMenuSub>
        <DropdownMenuItem onClick={() => openColumnVisibilityModal()}><Columns className="mr-2 h-3.5 w-3.5" />列显示与排序</DropdownMenuItem>
        <DropdownMenuItem onClick={handleOpenGroupConfig}><BarChart3 className="mr-2 h-3.5 w-3.5" />分组设置</DropdownMenuItem>
        <DropdownMenuItem onClick={() => openStyleEditorModal()}><Palette className="mr-2 h-3.5 w-3.5" />车卡样式</DropdownMenuItem>

        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={triggerImport}><FileUp className="mr-2 h-3.5 w-3.5" />导入样式</DropdownMenuItem>
        <DropdownMenuItem onClick={exportSettings}><FileDown className="mr-2 h-3.5 w-3.5" />导出样式</DropdownMenuItem>
        <DropdownMenuItem onClick={resetAllSettings}><RotateCcw className="mr-2 h-3.5 w-3.5" />恢复默认</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  useEffect(() => {
    if (isSettingsLoaded && taskListBoundaryRef.current && fabRef.current) {
      const containerRect = taskListBoundaryRef.current.getBoundingClientRect();
      const fabCurrentRef = fabRef.current;
      if (containerRect.width > 0 && containerRect.height > 0 && fabCurrentRef) {
        const fabRect = fabCurrentRef.getBoundingClientRect();
      }
    }
  }, [isSettingsLoaded, displayMode, filteredTasks.length, fabPosition]);

  useEffect(() => {
    if (isSettingsLoaded && !didShowResizeHintRef.current && displayMode === 'table') {
      didShowResizeHintRef.current = true;
      toast({
        title: "列宽调整提示",
        description: "您可以通过拖动列标题右侧边缘来调整列宽。",
        duration: 5000,
      });
    }
  }, [isSettingsLoaded, displayMode, toast]);

  const handleColumnSizingChange: OnChangeFn<ColumnSizingState> = (updater) => {
    const newSizing = typeof updater === 'function' ? updater(settings.columnWidths) : updater;
    updateSetting('columnWidths', newSizing);
  };

  const renderContent = () => {
    if (!isSettingsLoaded || isLoadingPlantInfo) {
      return <div className="h-full flex items-center justify-center"><p>加载中...</p></div>;
    }
    if (displayMode === 'table') {
      if (!selectedPlantId && filteredTasks.length === 0) {
        return <div className="flex items-center justify-center h-full text-muted-foreground p-4">请先选择一个搅拌站。</div>;
      }
      if (selectedPlantId && filteredTasks.length === 0) {
        return <div className="flex items-center justify-center h-full text-muted-foreground p-4">此搅拌站当前状态无任务。</div>;
      }

      const tableContent = (
        <VirtualizedTable
          data={expandedTasks}
          columns={tableColumns}
          getRowId={(row) => row.id}
          densityStyles={currentDensityValues}
          enableZebraStriping={enableZebraStriping}
          estimateRowHeight={estimateRowHeight}
          totalTableWidth={tableTotalWidth + (window?.innerWidth ? window.innerWidth * 0.05 : 0)}
          columnSizing={settings.columnWidths}
          onColumnSizingChange={handleColumnSizingChange}
          columnVisibility={settings.columnVisibility}
          onColumnVisibilityChange={(updater) => updateSetting('columnVisibility', typeof updater === 'function' ? updater(settings.columnVisibility) : updater)}
          columnOrder={columnOrder}
          onColumnOrderChange={(updater) => {
            const newOrder = typeof updater === 'function' 
              ? updater(columnOrder) 
              : updater;
            handleColumnOrderChange(newOrder);
          }}

          onHeaderContextMenu={handleHeaderContextMenu}
          onHeaderDoubleClick={handleHeaderDoubleClick}
          onRowContextMenu={(e, row) => openTaskContextMenu(e, (row.original as Task).id)}
          onRowDoubleClick={(row) => openTankerNoteModal(row.original as Task)}
          getColumnBackgroundProps={getColumnBackgroundProps}
          onDropOnProductionLine={handleDropOnProductionLine}
        />
      );

      return (
        <div className={cn(
          "flex flex-col",
          settings.groupConfig.enabled ? "h-auto min-h-full" : "h-full"
        )}>

          
          {/* 表格内容 */}
          <div className={cn(
            "flex-1 custom-scrollbar",
            settings.groupConfig.enabled ? "overflow-visible h-auto grouped-mode-container" : "overflow-auto"
          )}>
            {settings.groupConfig.enabled ? (
              <div className="space-y-0 min-h-full">
                {taskGroups.map((group) => (
                  <div key={`group-${group.key}-${group.tasks.length}`}>
                    <TaskGroupHeader
                      group={group}
                      groupConfig={settings.groupConfig}
                      onToggleCollapse={handleToggleGroupCollapse}
                      onCancelGrouping={handleCancelGrouping}
                    />
                    {!group.collapsed && group.tasks.length > 0 && (
                      <div className="relative h-auto min-h-0" key={`table-${group.key}`} style={{ minHeight: 'fit-content' }}>
                        <OptimizedGroupTable
                          tasks={group.tasks}
                          columns={tableColumns}
                          densityStyles={currentDensityValues}
                          enableZebraStriping={enableZebraStriping}
                          estimateRowHeight={estimateRowHeight}
                          totalTableWidth={tableTotalWidth + (window?.innerWidth ? window.innerWidth * 0.05 : 0)}
                          columnSizing={settings.columnWidths}
                          onColumnSizingChange={handleColumnSizingChange}
                          columnVisibility={settings.columnVisibility}
                          onColumnVisibilityChange={(updater) => updateSetting('columnVisibility', typeof updater === 'function' ? updater(settings.columnVisibility) : updater)}
                          columnOrder={columnOrder}
                          onColumnOrderChange={(updater) => {
                            const newOrder = typeof updater === 'function' 
                              ? updater(columnOrder) 
                              : updater;
                            handleColumnOrderChange(newOrder);
                          }}
                          onHeaderContextMenu={handleHeaderContextMenu}
                          onHeaderDoubleClick={handleHeaderDoubleClick}
                          onRowContextMenu={(e, row) => openTaskContextMenu(e, (row.original as Task).id)}
                          onRowDoubleClick={(row) => openTankerNoteModal(row.original as Task)}
                          getColumnBackgroundProps={(columnId) => getColumnBackgroundProps(columnId, false, false)}
                          onDropOnProductionLine={(taskId, lineIndex) => {
                            // 根据 lineIndex 构造生产线ID
                            const lineId = `L${lineIndex + 1}`;
                            // 从拖拽上下文获取 vehicle 对象
                            const draggedVehicle = allVehicles.find(v => v.isDragging);
                            if (draggedVehicle) {
                              handleDropOnProductionLine(draggedVehicle, taskId, lineId);
                            }
                          }}
                          isGroupedMode={true}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              tableContent
            )}
          </div>
        </div>
      );

    } else if (displayMode === 'card') {
      return <EnhancedTaskCardView
        filteredTasks={filteredTasks}
        vehicles={allVehicles}
        settings={settings}
        productionLineCount={productionLineCount}
        vehicleDisplayMode={vehicleDisplayMode}
        taskStatusFilter={taskStatusFilter}
        cardConfig={cardConfig}
        onCardConfigChange={(config) => {
          // 过滤掉 extra-large 选项,确保 size 只能是 small/medium/large
          const { size, ...rest } = config;
          setCardConfig({
            ...rest,
            size: size === 'extra-large' ? 'large' : size
          });
        } }

        onCancelVehicleDispatch={cancelVehicleDispatch}
        onOpenDeliveryOrderDetailsForVehicle={(vId, tId) => openDeliveryOrderDetailsModal(allVehicles.find(v => v.id === vId)!, allTasks.find(t => t.id === tId)!)}
        onOpenVehicleCardContextMenu={openVehicleCardContextMenu}
        onTaskContextMenu={(e, task) => openTaskContextMenu(e, task.id)}
        onTaskDoubleClick={openTankerNoteModal}
        onOpenStyleEditor={openStyleEditorModal}
        onDropVehicleFromPanelOnTaskCard={(vehicle, taskId) => handleDropOnProductionLine(vehicle, taskId, 'L1')}
        onDropVehicleOnLine={handleDropOnProductionLine}
        groupConfig={settings.groupConfig}
        onToggleGroupCollapse={handleToggleGroupCollapse}
        onCancelGrouping={handleCancelGrouping}
        taskCardConfig={taskCardConfig}
        onTaskCardConfigChange={handleTaskCardConfigChange} onOpenCardConfigModal={function (): void {
          // 打开任务卡片配置模态框
          setTaskCardConfigModalOpen(true);
        } }      />;
    }
    return null;
  };

  const cardGridContainerClasses = density === 'compact' ? "gap-px p-px"
    : density === 'normal' ? "gap-0.5 p-0.5"
      : "gap-1 p-1";


  return (
    <div className="h-full"
      >
      <SectionContainer
        title="任务列表"
        fabRender={fabComponent}
        hideHeader={displayMode === 'table'}
        contentClassName={cn(
          displayMode !== 'table'
            ? cardGridContainerClasses
            : 'p-0 h-full',
          isOverTaskList && canDropOnTaskList && "bg-primary/10 ring-2 ring-primary"
        )}
        containerRef={taskListBoundaryRef}
        actionButtons={renderHeaderActions()}
      >
        {renderContent()}
        <TaskListModals
          isTankerNoteModalOpen={isTankerNoteModalOpen}
          closeTankerNoteModal={closeTankerNoteModal}
          selectedTaskForTankerNote={selectedTaskForTankerNote}
          isColumnVisibilityModalOpen={isColumnVisibilityModalOpen}
          closeColumnVisibilityModal={closeColumnVisibilityModal}
          allColumns={ALL_TASK_COLUMNS_CONFIG}
          columnVisibility={settings.columnVisibility}
          handleColumnVisibilityChange={handleColumnVisibilityChange}
          currentOrder={settings.columnOrder}
          handleColumnOrderChange={(newOrder) => updateSetting('columnOrder', newOrder)}
          isColumnSpecificStyleModalOpen={isColumnSpecificStyleModalOpen}
          closeColumnSpecificStyleModal={closeColumnSpecificStyleModal}
          editingColumnDef={editingColumnDef}
          columnTextStyles={settings.columnTextStyles as Record<StyleableColumnId, ColumnTextStyle | undefined>}
          columnBackgrounds={settings.columnBackgrounds}
          handleColumnTextStyleChange={handleColumnTextStyleChange}
          handleColumnBackgroundChange={handleColumnBackgroundChange}
          isStyleEditorModalOpen={isStyleEditorModalOpen}
          closeStyleEditorModal={closeStyleEditorModal}
          inTaskVehicleCardStyles={settings.inTaskVehicleCardStyles}
          updateSetting={updateSetting}
          isDeliveryOrderDetailsModalOpen={isDeliveryOrderDetailsModalOpen}
          closeDeliveryOrderDetailsModal={closeDeliveryOrderDetailsModal}
          selectedVehicleForDeliveryOrder={selectedVehicleForDeliveryOrder}
          selectedTaskForDeliveryOrder={selectedTaskForDeliveryOrder}
          isReminderConfigModalOpen={isReminderConfigModalOpen}
          selectedTaskForReminderConfig={selectedTaskForReminderConfig}
          closeReminderConfigModal={closeReminderConfigModal}
          onVehiclesPerRowChange={handleVehiclesPerRowChange}
        />
        

        
        {/* 任务卡片配置模态框 */}
        <TaskCardConfigModal
          open={taskCardConfigModalOpen}
          onOpenChange={setTaskCardConfigModalOpen}
          config={taskCardConfig}
          onConfigChange={handleTaskCardConfigChange}
        />

        {/* 分组配置模态框 */}
        <TaskGroupConfigModal
          isOpen={isGroupConfigModalOpen}
          onClose={() => setIsGroupConfigModalOpen(false)}
          groupConfig={settings.groupConfig}
          onUpdateConfig={handleUpdateGroupConfig}
        />
        <TaskListContextMenus
          isTaskContextMenuOpen={isTaskContextMenuOpen}
          taskContextMenuPosition={taskContextMenuPosition}
          contextMenuTaskData={contextMenuTaskData}
          closeTaskContextMenu={closeTaskContextMenu}
          openTankerNoteModal={openTankerNoteModal}
          openReminderConfigModal={openReminderConfigModal}
          filteredTasks={filteredTasks}
          isVehicleCardContextMenuOpen={isVehicleCardContextMenuOpen}
          vehicleCardContextMenuPosition={vehicleCardContextMenuPosition}
          vehicleCardContextMenuContext={vehicleCardContextMenuContext}
          closeVehicleCardContextMenu={closeVehicleCardContextMenu}
          openDeliveryOrderDetailsModal={openDeliveryOrderDetailsModal}
          cancelVehicleDispatch={cancelVehicleDispatch}
        />


        <input type="file" accept=".json" ref={fileInputRef} onChange={(e) => { if (e.target.files && e.target.files.length > 0) { settings.handleImportFile(e) } }} style={{ display: 'none' }} />
      </SectionContainer>
    </div>
  );
}
